# 🎨 T-Shirt Design Generator

一个现代化的 T 恤设计生成器网站，使用 Next.js 15 和 Tailwind CSS 4 构建。

## ✨ 功能特点

### 🎯 核心功能
- **智能设计生成器**: 基于文本描述生成 T 恤设计
- **多样化设计风格**: 支持复古、现代、简约等多种风格
- **实时预览**: 即时查看设计效果
- **响应式设计**: 完美适配桌面和移动设备

### 🎨 设计工具
- **文本输入**: 描述您的设计想法
- **风格选择**: 多种预设设计风格
- **设计指令**: 自定义设计特征
- **图片生成**: AI 驱动的图像生成

### 📱 用户体验
- **现代化界面**: 清爽的橙色-粉色主题
- **流畅动画**: 精心设计的交互动效
- **优化表单**: 美观的单层聚焦边框
- **快速加载**: 静态生成优化

## 🚀 技术栈

### 前端框架
- **Next.js 15.2.3**: React 全栈框架
- **React 19**: 最新的 React 版本
- **TypeScript**: 类型安全的 JavaScript

### 样式和 UI
- **Tailwind CSS 4.0**: 实用优先的 CSS 框架
- **自定义动画**: CSS 动画和过渡效果
- **响应式设计**: 移动优先的设计方法

### 开发工具
- **ESLint**: 代码质量检查
- **PostCSS**: CSS 后处理器
- **Turbopack**: 快速的构建工具

## 🛠️ 快速开始

### 环境要求
- Node.js 18.0 或更高版本
- npm、yarn 或 pnpm

### 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看网站。

### 构建生产版本
```bash
npm run build
npm run start
```

## 📁 项目结构

```
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── globals.css      # 全局样式
│   │   ├── layout.tsx       # 根布局
│   │   └── page.tsx         # 首页
│   ├── components/          # React 组件
│   │   ├── Header.tsx       # 网站头部
│   │   ├── Hero.tsx         # 英雄区域
│   │   ├── TShirtDesigner.tsx # 设计器组件
│   │   ├── ImageGenerator.tsx # 图片生成器
│   │   ├── DesignGallery.tsx  # 设计画廊
│   │   ├── SimpleSteps.tsx    # 使用步骤
│   │   ├── FAQ.tsx           # 常见问题
│   │   ├── Footer.tsx        # 网站底部
│   │   └── Analytics.tsx     # 分析组件
│   └── styles/              # 样式文件
├── public/                  # 静态资源
├── docs/                    # 文档文件
└── 配置文件...
```

## 🎨 设计系统

### 颜色主题
- **主色调**: 橙色到粉色渐变 (`from-orange-500 to-pink-500`)
- **背景色**: 蓝色到紫色渐变 (`from-blue-50 via-white to-purple-50`)
- **文本色**: 深灰色 (`text-gray-900`)
- **边框色**: 浅灰色 (`border-gray-300`)

### 聚焦样式
- **单层边框**: 橙色聚焦边框 (`focus:border-orange-500`)
- **无双重效果**: 移除了 ring 和 outline
- **平滑过渡**: 200ms 过渡动画

### 动画效果
- **Blob 动画**: 背景装饰动画
- **滚动动画**: 设计画廊滚动效果
- **悬停效果**: 按钮和卡片悬停动画

## 📊 性能优化

### 构建优化
- **静态生成**: 预渲染所有页面
- **代码分割**: 自动代码分割
- **图片优化**: Next.js Image 组件
- **CSS 优化**: Tailwind CSS 压缩

### 加载性能
- **首屏加载**: ~107KB JavaScript
- **静态资源**: 优化的图片和字体
- **缓存策略**: 浏览器和 CDN 缓存

## 🌐 部署

### Vercel 部署（推荐）
详细部署指南请查看 [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md)

```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署
vercel

# 生产环境部署
vercel --prod
```

### 其他平台
- **Netlify**: 支持静态部署
- **AWS Amplify**: 支持全栈部署
- **Docker**: 容器化部署

## 🔧 配置

### 环境变量
复制 `.env.example` 到 `.env.local` 并配置：

```bash
cp .env.example .env.local
```

### 图片域名配置
在 `next.config.ts` 中配置允许的图片域名：

```typescript
images: {
  remotePatterns: [
    {
      protocol: 'https',
      hostname: 'your-image-domain.com',
    },
  ],
}
```

## 📈 SEO 优化

### 已实现的 SEO 功能
- **元数据**: 完整的页面元数据
- **结构化数据**: JSON-LD 标记
- **Sitemap**: 自动生成站点地图
- **Robots.txt**: 搜索引擎爬虫配置
- **Open Graph**: 社交媒体分享优化

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您有任何问题或建议，请：

- 创建 [Issue](https://github.com/yourusername/t-shirt-design-generator/issues)
- 发送邮件到 <EMAIL>
- 查看 [文档](./docs/)

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Vercel](https://vercel.com/) - 部署平台
- [Unsplash](https://unsplash.com/) - 图片资源

---

⭐ 如果这个项目对您有帮助，请给它一个星标！
