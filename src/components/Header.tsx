'use client';

import { useState } from 'react';
import { LogoWithText } from './Logo';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <LogoWithText size="md" variant="default" />
            </div>
          </div>

          {/* Desktop Navigation - Centered */}
          <nav className="hidden md:flex items-center space-x-8 mx-auto">
            <a href="#tshirt-designer" className="text-gray-800 hover:text-orange-600 transition-colors duration-200 font-bold text-base tracking-wide">
              Designer
            </a>
            <a href="#gallery" className="text-gray-800 hover:text-orange-600 transition-colors duration-200 font-bold text-base tracking-wide">
              Gallery
            </a>
            <a href="#faq" className="text-gray-800 hover:text-orange-600 transition-colors duration-200 font-bold text-base tracking-wide">
              FAQ
            </a>
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-gray-900 focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200">
              <a href="#tshirt-designer" className="block px-3 py-2 text-gray-800 hover:text-orange-600 font-bold">
                Designer
              </a>
              <a href="#gallery" className="block px-3 py-2 text-gray-800 hover:text-orange-600 font-bold">
                Gallery
              </a>
              <a href="#faq" className="block px-3 py-2 text-gray-800 hover:text-orange-600 font-bold">
                FAQ
              </a>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
