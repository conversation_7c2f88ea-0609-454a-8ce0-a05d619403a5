'use client';

import { useState } from 'react';

export default function Pricing() {
  const [isAnnual, setIsAnnual] = useState(true);

  const plans = [
    {
      name: 'Free',
      description: 'Start your AI creation journey',
      price: { monthly: 0, annual: 0 },
      features: [
        '10 image generation credits per month',
        'Access to basic AI models',
        'Standard definition output',
        'Limited style options',
        'Watermarked downloads',
        'Community support'
      ],
      limitations: [
        'No commercial use',
        'Watermarked images',
        'Limited resolution'
      ],
      cta: 'Use Now',
      popular: false,
      color: 'border-slate-200'
    },
    {
      name: 'Basic',
      description: 'Great for hobbyists and casual creators',
      price: { monthly: 9.9, annual: 99.9 },
      originalPrice: { monthly: 11.9, annual: 118.8 },
      features: [
        '50 image generation credits per month',
        'Access to all AI models',
        'High-definition output',
        'All style options',
        'Watermark-free downloads',
        'Email support'
      ],
      cta: 'Buy Now',
      popular: false,
      color: 'border-slate-200'
    },
    {
      name: 'Pro',
      description: 'Ideal for content creators and small businesses',
      price: { monthly: 19.9, annual: 199.9 },
      originalPrice: { monthly: 23.9, annual: 238.8 },
      features: [
        '150 image generation credits per month',
        'Priority processing queue',
        'Batch image generation',
        'Advanced style presets',
        'Cloud image storage',
        'API access',
        'Priority customer support'
      ],
      cta: 'Buy Now',
      popular: true,
      color: 'border-purple-500'
    },
    {
      name: 'Ultra',
      description: 'Perfect for agencies and large teams',
      price: { monthly: 29.9, annual: 299.9 },
      originalPrice: { monthly: 35.9, annual: 358.8 },
      features: [
        '500 image generation credits per month',
        'Professional-grade model access',
        'Custom style training',
        '4K ultra-high-definition output',
        'Commercial licensing',
        'Team collaboration features',
        'Dedicated technical advisor'
      ],
      cta: 'Buy Now',
      popular: false,
      color: 'border-slate-200'
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 tracking-tight">
            Pricing
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
            Choose the perfect credit package for your AI image generation needs and unlock unlimited creative possibilities.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4">
            <span className={`text-sm font-medium ${!isAnnual ? 'text-slate-900' : 'text-slate-500'}`}>
              Monthly
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                isAnnual ? 'bg-purple-600' : 'bg-slate-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAnnual ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm font-medium ${isAnnual ? 'text-slate-900' : 'text-slate-500'}`}>
              Annually
            </span>
            <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded-full text-xs font-semibold">
              Save 16%
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {plans.map((plan, index) => (
            <div
              key={plan.name}
              className={`relative bg-white rounded-2xl shadow-lg border-2 ${plan.color} p-8 ${
                plan.popular ? 'transform scale-105' : ''
              } transition-all duration-300 hover:shadow-xl`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-purple-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Popular
                  </span>
                </div>
              )}

              <div className="text-center">
                <h3 className="text-xl font-bold text-slate-900 mb-2">{plan.name}</h3>
                <p className="text-slate-600 text-sm mb-6">{plan.description}</p>

                <div className="mb-6">
                  {plan.price.monthly === 0 ? (
                    <div className="text-3xl font-bold text-slate-900">Free</div>
                  ) : (
                    <div>
                      {plan.originalPrice && (
                        <div className="text-sm text-slate-500 line-through">
                          ${isAnnual ? plan.originalPrice.annual : plan.originalPrice.monthly}
                          {isAnnual ? '/year' : '/month'}
                        </div>
                      )}
                      <div className="text-3xl font-bold text-slate-900">
                        ${isAnnual ? plan.price.annual : plan.price.monthly}
                        <span className="text-sm font-normal text-slate-500">
                          {isAnnual ? '/year' : '/month'}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                <button
                  className={`w-full py-3 px-4 rounded-xl font-semibold transition-all duration-200 mb-6 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl'
                      : plan.name === 'Free'
                      ? 'bg-slate-100 text-slate-700 hover:bg-slate-200'
                      : 'bg-slate-900 text-white hover:bg-slate-800'
                  }`}
                >
                  {plan.cta}
                </button>

                <ul className="space-y-3 text-left">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <svg className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm text-slate-600">{feature}</span>
                    </li>
                  ))}
                  {plan.limitations && plan.limitations.map((limitation, limitIndex) => (
                    <li key={`limit-${limitIndex}`} className="flex items-start">
                      <svg className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm text-slate-500">{limitation}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-slate-600">
            All plans include commercial usage rights and priority customer support.
          </p>
        </div>
      </div>
    </section>
  );
}
