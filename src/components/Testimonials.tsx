'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      role: 'Digital Artist',
      avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
      content: 'This AI image generator revolutionized my creative process. I can generate concept art in minutes instead of hours, and the quality is absolutely stunning!'
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'Marketing Director',
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
      content: 'We use the AI image generator for all our marketing visuals. The quality is professional-grade and saves us thousands on stock photos and design work.'
    },
    {
      id: 3,
      name: '<PERSON>',
      role: 'Game Developer',
      avatar: 'https://randomuser.me/api/portraits/women/3.jpg',
      content: 'The variety of AI models available is incredible. We can create consistent art styles for our games while maintaining high quality and fast turnaround.'
    },
    {
      id: 4,
      name: '<PERSON>',
      role: 'Content Creator',
      avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
      content: 'The AI image generator helps me create unique thumbnails and visuals for my YouTube channel. The commercial licensing gives me peace of mind for all content.'
    },
    {
      id: 5,
      name: 'Lisa Wang',
      role: 'UI/UX Designer',
      avatar: 'https://randomuser.me/api/portraits/women/5.jpg',
      content: 'I use the AI image generator for inspiration and concept development. The ability to rapidly iterate on ideas has made my design process so much more efficient.'
    },
    {
      id: 6,
      name: 'Alex Johnson',
      role: 'Startup Founder',
      avatar: 'https://randomuser.me/api/portraits/women/6.jpg',
      content: 'We needed professional product visuals quickly, and the AI image generator delivered. The quality is amazing and helped us launch our product on time.'
    }
  ];

  // Auto-advance testimonials
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [testimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Testimonial
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Hear from designers, artists, and creators who use Seedream AI Image Generator for their creative projects.
          </p>
        </div>

        <div className="relative">
          {/* Main testimonial display */}
          <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-2xl p-8 md:p-12 shadow-lg">
            <div className="max-w-4xl mx-auto text-center">
              <div className="mb-8">
                <svg className="w-12 h-12 text-purple-600 mx-auto mb-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                </svg>
                <blockquote className="text-xl md:text-2xl text-gray-700 font-medium leading-relaxed">
                  "{testimonials[currentIndex].content}"
                </blockquote>
              </div>
              
              <div className="flex items-center justify-center">
                <div className="relative w-16 h-16 rounded-full overflow-hidden mr-4">
                  <Image
                    src={testimonials[currentIndex].avatar}
                    alt={testimonials[currentIndex].name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="text-left">
                  <div className="font-semibold text-gray-900 text-lg">
                    {testimonials[currentIndex].name}
                  </div>
                  <div className="text-gray-600">
                    {testimonials[currentIndex].role}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-purple-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={nextTestimonial}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-purple-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        {/* Dots indicator */}
        <div className="flex justify-center mt-8 space-x-2">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentIndex
                  ? 'bg-purple-600 scale-110'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>

        {/* Scrolling testimonials preview */}
        <div className="mt-16 overflow-hidden">
          <div className="flex space-x-6 animate-scroll">
            {[...testimonials, ...testimonials].map((testimonial, index) => (
              <div key={`scroll-${index}`} className="flex-shrink-0 w-80 bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center mb-4">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden mr-3">
                    <Image
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.role}</div>
                  </div>
                </div>
                <p className="text-gray-700 text-sm line-clamp-4">{testimonial.content}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
