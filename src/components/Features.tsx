export default function Features() {
  const features = [
    {
      icon: "🤖",
      title: "AI-Powered Design",
      description: "Advanced AI technology creates unique designs from your text descriptions in seconds.",
      color: "from-blue-500 to-purple-500",
      benefits: ["Instant generation", "Unique every time", "Professional quality"]
    },
    {
      icon: "🎨",
      title: "Multiple Styles",
      description: "Choose from streetwear, vintage, minimalist, cartoon, abstract, and typography styles.",
      color: "from-purple-500 to-pink-500",
      benefits: ["6 design styles", "Trending aesthetics", "Style mixing"]
    },
    {
      icon: "💯",
      title: "100% Free",
      description: "Create unlimited designs without any cost. No hidden fees, no subscriptions required.",
      color: "from-green-500 to-blue-500",
      benefits: ["Unlimited designs", "No watermarks", "Free downloads"]
    },
    {
      icon: "📱",
      title: "Mobile Friendly",
      description: "Design on any device - desktop, tablet, or mobile. Responsive design that works everywhere.",
      color: "from-orange-500 to-red-500",
      benefits: ["Any device", "Touch optimized", "Fast loading"]
    },
    {
      icon: "⚡",
      title: "Lightning Fast",
      description: "Generate high-quality t-shirt designs in under 10 seconds. No waiting, instant results.",
      color: "from-yellow-500 to-orange-500",
      benefits: ["Under 10 seconds", "No queues", "Instant preview"]
    },
    {
      icon: "🎯",
      title: "Print Ready",
      description: "Download high-resolution files ready for printing on any t-shirt or merchandise.",
      color: "from-pink-500 to-red-500",
      benefits: ["High resolution", "Print optimized", "Multiple formats"]
    }
  ];

  const stats = [
    { number: "50K+", label: "Designs Created", icon: "🎨" },
    { number: "10K+", label: "Happy Users", icon: "😊" },
    { number: "6", label: "Design Styles", icon: "🎭" },
    { number: "100%", label: "Free Forever", icon: "💯" }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 tracking-tight">
            ✨ Why Choose Our T-Shirt Designer?
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed max-w-2xl mx-auto">
            The most advanced and user-friendly AI t-shirt design generator on the web
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <div key={index} className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              {/* Icon */}
              <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center text-2xl mb-6 shadow-lg`}>
                {feature.icon}
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                {feature.title}
              </h3>
              
              <p className="text-gray-600 mb-4 leading-relaxed">
                {feature.description}
              </p>

              {/* Benefits */}
              <ul className="space-y-2">
                {feature.benefits.map((benefit, idx) => (
                  <li key={idx} className="flex items-center text-sm text-gray-700">
                    <span className="w-2 h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full mr-3"></span>
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            🚀 Trusted by Thousands
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl mb-2">{stat.icon}</div>
                <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 text-sm">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-orange-50 to-pink-50 rounded-3xl p-8 border border-orange-100">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              🎯 Ready to Experience the Difference?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join thousands of creators who have already discovered the easiest way to design amazing t-shirts.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href="#tshirt-designer"
                className="bg-gradient-to-r from-orange-500 to-pink-500 text-white px-8 py-4 rounded-full hover:from-orange-600 hover:to-pink-600 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                🚀 Start Designing Free
              </a>
              
              <div className="flex items-center text-sm text-gray-500">
                <span className="mr-2">⚡</span>
                No credit card • No sign-up • Instant access
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
