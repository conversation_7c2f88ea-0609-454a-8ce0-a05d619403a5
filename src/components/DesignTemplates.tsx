'use client';

export default function DesignTemplates() {
  const templates = [
    {
      id: 1,
      title: "Vintage Sunset",
      style: "Vintage",
      description: "Retro sunset with palm trees",
      image: "/api/placeholder/300/300",
      prompt: "Vintage sunset with palm trees and retro waves in warm orange and pink colors",
      tags: ["Vintage", "Sunset", "Retro"],
      color: "from-orange-400 to-pink-400"
    },
    {
      id: 2,
      title: "Street Art Dragon",
      style: "Streetwear",
      description: "Cool graffiti-style dragon",
      image: "/api/placeholder/300/300",
      prompt: "Street art graffiti dragon breathing fire with urban neon colors",
      tags: ["Streetwear", "Dragon", "Graffiti"],
      color: "from-purple-400 to-blue-400"
    },
    {
      id: 3,
      title: "Minimal Mountains",
      style: "Minimalist",
      description: "Clean geometric landscape",
      image: "/api/placeholder/300/300",
      prompt: "Minimalist geometric mountain landscape with simple lines and shapes",
      tags: ["Minimalist", "Mountains", "Geometric"],
      color: "from-blue-400 to-green-400"
    },
    {
      id: 4,
      title: "Cartoon Cat",
      style: "Cartoon",
      description: "Cute cat with sunglasses",
      image: "/api/placeholder/300/300",
      prompt: "Cute cartoon cat wearing sunglasses with playful colors",
      tags: ["Cartoon", "Cat", "Fun"],
      color: "from-pink-400 to-purple-400"
    },
    {
      id: 5,
      title: "Abstract Waves",
      style: "Abstract",
      description: "Colorful flowing patterns",
      image: "/api/placeholder/300/300",
      prompt: "Abstract colorful waves and flowing patterns in vibrant colors",
      tags: ["Abstract", "Waves", "Colorful"],
      color: "from-green-400 to-blue-400"
    },
    {
      id: 6,
      title: "Bold Typography",
      style: "Typography",
      description: "Stay Wild motivational quote",
      image: "/api/placeholder/300/300",
      prompt: "Bold typography design with 'Stay Wild' text in modern font",
      tags: ["Typography", "Quote", "Bold"],
      color: "from-red-400 to-orange-400"
    }
  ];

  const handleUseTemplate = (template: any) => {
    // Scroll to designer and fill in the prompt
    const designerSection = document.getElementById('tshirt-designer');
    if (designerSection) {
      designerSection.scrollIntoView({ behavior: 'smooth' });
      // In a real app, this would set the prompt in the designer component
      setTimeout(() => {
        const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
        if (textarea) {
          textarea.value = template.prompt;
          textarea.focus();
        }
      }, 1000);
    }
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 tracking-tight">
            🎨 Popular Design Templates
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed max-w-2xl mx-auto">
            Get inspired by these trending designs or use them as starting points for your own creations
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {templates.map((template) => (
            <div key={template.id} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              {/* Template Image */}
              <div className="relative h-64 bg-gray-100 overflow-hidden">
                <div className={`absolute inset-0 bg-gradient-to-br ${template.color} opacity-20`}></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center p-8">
                    <div className="text-4xl mb-4">🎨</div>
                    <div className="text-lg font-bold text-gray-800">{template.title}</div>
                    <div className="text-sm text-gray-600 mt-2">{template.description}</div>
                  </div>
                </div>
                
                {/* Style Badge */}
                <div className="absolute top-4 left-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-semibold text-white bg-gradient-to-r ${template.color} shadow-lg`}>
                    {template.style}
                  </span>
                </div>
              </div>

              {/* Template Info */}
              <div className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  {template.title}
                </h3>
                
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {template.prompt}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {template.tags.map((tag, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Use Template Button */}
                <button
                  onClick={() => handleUseTemplate(template)}
                  className={`w-full py-3 rounded-xl font-semibold text-white bg-gradient-to-r ${template.color} hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1`}
                >
                  Use This Template
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* More Templates CTA */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Want More Templates?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              We're constantly adding new design templates. Create your own unique designs or browse our growing collection.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#tshirt-designer"
                className="bg-gradient-to-r from-orange-500 to-pink-500 text-white px-8 py-4 rounded-full hover:from-orange-600 hover:to-pink-600 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                🚀 Create Custom Design
              </a>
              
              <button className="bg-white text-gray-700 px-8 py-4 rounded-full border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                📧 Get New Templates
              </button>
            </div>
          </div>
        </div>

        {/* Template Categories */}
        <div className="mt-16">
          <h3 className="text-xl font-bold text-gray-900 mb-8 text-center">Browse by Style</h3>
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            {['Streetwear', 'Vintage', 'Minimalist', 'Typography', 'Cartoon', 'Abstract'].map((style, index) => {
              const colors = [
                'from-orange-400 to-pink-400',
                'from-purple-400 to-blue-400',
                'from-blue-400 to-green-400',
                'from-pink-400 to-purple-400',
                'from-green-400 to-blue-400',
                'from-red-400 to-orange-400'
              ];
              
              return (
                <button
                  key={style}
                  className={`p-4 rounded-xl bg-gradient-to-r ${colors[index]} text-white font-semibold text-sm hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1`}
                >
                  {style}
                </button>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
