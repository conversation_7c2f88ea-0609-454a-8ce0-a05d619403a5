export default function SimpleSteps() {
  const steps = [
    {
      number: 1,
      title: "Describe Your Design Idea",
      description: "Tell us what you want on your tshirt design. Be as creative as you like!",
      icon: "💭",
      example: "A cool dragon breathing fire",
      color: "from-blue-500 to-purple-500"
    },
    {
      number: 2,
      title: "Choose Your Style",
      description: "Pick from streetwear, vintage, minimalist, and more design styles.",
      icon: "🎨",
      example: "Streetwear style",
      color: "from-purple-500 to-pink-500"
    },
    {
      number: 3,
      title: "Download Your Design",
      description: "Get your high-quality design instantly. Ready for printing!",
      icon: "⬇️",
      example: "Print-ready file",
      color: "from-pink-500 to-orange-500"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 tracking-tight">
            How Our Tshirt Design Generator Works
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed max-w-2xl mx-auto">
            Create professional tshirt designs in just 3 simple steps with our tshirt design generator
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
          {steps.map((step, index) => (
            <div key={step.number} className="relative">
              {/* Connection Line */}
              {index < steps.length - 1 && (
                <div className="hidden md:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-gray-200 to-gray-300 z-0" 
                     style={{ width: 'calc(100% - 2rem)', left: '2rem' }} />
              )}
              
              <div className="relative bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                {/* Step Number */}
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${step.color} flex items-center justify-center text-white font-bold text-xl mb-6 mx-auto shadow-lg`}>
                  {step.number}
                </div>

                {/* Icon */}
                <div className="text-4xl text-center mb-4">
                  {step.icon}
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-gray-900 mb-3 text-center">
                  {step.title}
                </h3>
                
                <p className="text-gray-600 text-center mb-4 leading-relaxed">
                  {step.description}
                </p>

                {/* Example */}
                <div className="bg-gray-50 rounded-lg p-3 text-center">
                  <span className="text-sm text-gray-500">Example:</span>
                  <div className="text-sm font-medium text-gray-700 mt-1">
                    "{step.example}"
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 border border-blue-100">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Create Your First Design?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join thousands of creators who have already designed amazing t-shirts with our AI tool.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href="#tshirt-designer"
                className="bg-gradient-to-r from-orange-500 to-pink-500 text-white px-8 py-4 rounded-full hover:from-orange-600 hover:to-pink-600 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                🚀 Start Designing Now
              </a>
              
              <div className="flex items-center text-sm text-gray-500">
                <span className="mr-2">✨</span>
                No signup required • 100% Free
              </div>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-2">50K+</div>
            <div className="text-gray-600">Designs Created</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-2">10K+</div>
            <div className="text-gray-600">Happy Users</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-2">6</div>
            <div className="text-gray-600">Design Styles</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-2">100%</div>
            <div className="text-gray-600">Free Forever</div>
          </div>
        </div>
      </div>
    </section>
  );
}
