'use client';

import { useState } from 'react';

export default function TShirtDesigner() {
  const [prompt, setPrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('');
  const [selectedColor, setSelectedColor] = useState('white');
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [showError, setShowError] = useState(false);

  const styles = [
    { id: 'streetwear', name: 'Streetwear', icon: '🏙️', description: 'Urban and edgy designs' },
    { id: 'vintage', name: 'Vintage', icon: '📻', description: 'Retro and classic vibes' },
    { id: 'minimalist', name: 'Minimalist', icon: '⚪', description: 'Clean and simple' },
    { id: 'typography', name: 'Typography', icon: '✍️', description: 'Text-based designs' },
    { id: 'cartoon', name: 'Cartoon', icon: '🎨', description: 'Fun and playful' },
    { id: 'abstract', name: 'Abstract', icon: '🌀', description: 'Artistic and creative' },
  ];

  const tshirtColors = [
    { id: 'white', name: 'White', color: '#FFFFFF', border: 'border-gray-300' },
    { id: 'black', name: 'Black', color: '#000000', border: 'border-gray-700' },
    { id: 'navy', name: 'Navy', color: '#1E3A8A', border: 'border-blue-700' },
    { id: 'gray', name: 'Gray', color: '#6B7280', border: 'border-gray-500' },
    { id: 'red', name: 'Red', color: '#DC2626', border: 'border-red-600' },
    { id: 'green', name: 'Green', color: '#059669', border: 'border-green-600' },
  ];

  const examplePrompts = [
    "Vintage sunset with palm trees and retro waves",
    "Minimalist geometric mountain landscape",
    "Cool street art graffiti with urban vibes",
    "Cute cartoon cat wearing sunglasses",
    "Abstract colorful paint splash design",
    "Typography quote: 'Stay Wild' in bold letters"
  ];

  const isFormValid = prompt.trim() && selectedStyle;

  const handleGenerate = () => {
    if (!isFormValid) return;

    setIsGenerating(true);
    setProgress(0);
    setShowModal(true);
    setShowError(false);

    // Simulate realistic progress - fast at first, then slower
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev < 30) return prev + Math.random() * 8 + 5; // Fast start: 5-13% per step
        if (prev < 60) return prev + Math.random() * 4 + 2; // Medium: 2-6% per step
        if (prev < 80) return prev + Math.random() * 2 + 1; // Slow: 1-3% per step
        if (prev < 90) return prev + Math.random() * 1 + 0.5; // Very slow: 0.5-1.5% per step
        return prev; // Stop at around 90%
      });
    }, 600);

    // Wait 30-40 seconds before showing error
    const waitTime = 30000 + Math.random() * 10000; // 30-40 seconds
    setTimeout(() => {
      clearInterval(progressInterval);

      // Set final progress between 60-80%
      const finalProgress = 60 + Math.random() * 20;
      setProgress(finalProgress);

      // Show error message in modal after a short delay
      setTimeout(() => {
        setShowError(true);
      }, 1500);
    }, waitTime);
  };

  const handleCloseModal = () => {
    setIsGenerating(false);
    setShowModal(false);
    setShowError(false);
    setProgress(0);
  };

  const handleExampleClick = (example: string) => {
    setPrompt(example);
  };

  return (
    <section id="tshirt-designer" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 tracking-tight">
            🎨 Tshirt Design Generator - Create Your Perfect Design
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed max-w-2xl mx-auto">
            Describe your tshirt design idea, choose a style, and watch our tshirt design generator create your custom design in seconds
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Design Form */}
          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
            {/* Step 1: Describe Your Design */}
            <div className="mb-8">
              <div className="flex items-center mb-4">
                <span className="bg-gradient-to-r from-orange-500 to-pink-500 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3">1</span>
                <h3 className="text-lg font-semibold text-gray-900">Describe Your Design</h3>
              </div>
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Describe your tshirt design idea for our tshirt design generator... (e.g., 'A cool dragon breathing fire with neon colors')"
                className="w-full px-4 py-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-0 focus:border-orange-500 resize-none transition-all duration-200 bg-gray-50 text-gray-900 placeholder-gray-400 hover:border-gray-400"
                style={{ outline: 'none', boxShadow: 'none' }}
                rows={4}
              />

              {/* Example prompts */}
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">💡 Try these ideas:</p>
                <div className="flex flex-wrap gap-2">
                  {examplePrompts.slice(0, 3).map((example, index) => (
                    <button
                      key={index}
                      onClick={() => handleExampleClick(example)}
                      className="text-xs bg-blue-50 text-blue-700 px-3 py-1 rounded-full hover:bg-blue-100 transition-colors border border-blue-200"
                    >
                      {example.length > 30 ? example.substring(0, 30) + '...' : example}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Step 2: Choose Style */}
            <div className="mb-8">
              <div className="flex items-center mb-4">
                <span className="bg-gradient-to-r from-orange-500 to-pink-500 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3">2</span>
                <h3 className="text-lg font-semibold text-gray-900">Choose Style</h3>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {styles.map((style) => (
                  <button
                    key={style.id}
                    onClick={() => setSelectedStyle(style.id)}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                      selectedStyle === style.id
                        ? 'border-orange-500 bg-orange-50 shadow-lg'
                        : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                    }`}
                  >
                    <div className="text-2xl mb-2">{style.icon}</div>
                    <div className="font-semibold text-gray-900 text-sm">{style.name}</div>
                    <div className="text-xs text-gray-600">{style.description}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Step 3: T-Shirt Color */}
            <div className="mb-8">
              <div className="flex items-center mb-4">
                <span className="bg-gradient-to-r from-orange-500 to-pink-500 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3">3</span>
                <h3 className="text-lg font-semibold text-gray-900">T-Shirt Color</h3>
              </div>
              <div className="flex flex-wrap gap-3">
                {tshirtColors.map((color) => (
                  <button
                    key={color.id}
                    onClick={() => setSelectedColor(color.id)}
                    className={`w-12 h-12 rounded-full border-4 transition-all duration-200 ${
                      selectedColor === color.id
                        ? 'border-orange-500 shadow-lg scale-110'
                        : `${color.border} hover:scale-105`
                    }`}
                    style={{ backgroundColor: color.color }}
                    title={color.name}
                  />
                ))}
              </div>
            </div>

            {/* Generate Button */}
            <button
              onClick={handleGenerate}
              disabled={!isFormValid || isGenerating}
              className={`w-full py-4 rounded-xl font-semibold text-lg transition-all duration-200 ${
                !isFormValid || isGenerating
                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-orange-500 to-pink-500 text-white hover:from-orange-600 hover:to-pink-600 shadow-lg hover:shadow-xl transform hover:-translate-y-1'
              }`}
            >
              {isGenerating ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating Your Design...
                </div>
              ) : (
                '🚀 Generate My T-Shirt Design'
              )}
            </button>

            {/* Info */}
            <div className="mt-4 text-center text-sm text-gray-500">
              <p>✨ Free forever • No account required • Download instantly</p>
            </div>
          </div>

          {/* T-Shirt Preview */}
          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-6 text-center">Preview</h3>

            {/* T-Shirt Mockup */}
            <div className="relative mx-auto w-64 h-80 mb-6">
              <div
                className="w-full h-full rounded-2xl shadow-lg flex items-center justify-center relative overflow-hidden"
                style={{
                  backgroundColor: tshirtColors.find(c => c.id === selectedColor)?.color || '#FFFFFF'
                }}
              >
                {/* T-Shirt Shape Overlay */}
                <div className="absolute inset-4 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                  {prompt ? (
                    <div className="text-center p-4">
                      <div className="text-2xl mb-2">🎨</div>
                      <div className="text-sm text-gray-600 font-medium">
                        Your "{selectedStyle}" design will appear here
                      </div>
                    </div>
                  ) : (
                    <div className="text-center p-4">
                      <div className="text-3xl mb-2">👕</div>
                      <div className="text-sm text-gray-500">
                        Describe your design to see preview
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Design Info */}
            <div className="bg-gray-50 rounded-xl p-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Style:</span>
                  <div className="font-semibold text-gray-900 capitalize">{selectedStyle}</div>
                </div>
                <div>
                  <span className="text-gray-600">Color:</span>
                  <div className="font-semibold text-gray-900 capitalize">{selectedColor}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Modal */}
      {showModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl p-8 max-w-md w-full mx-4 text-center shadow-2xl border border-white/20 relative">
            {/* Close Button */}
            <button
              onClick={handleCloseModal}
              className="absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 group"
            >
              <svg className="w-4 h-4 text-gray-500 group-hover:text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {!showError ? (
              <>
                <div className="mb-8">
                  <div className="w-20 h-20 mx-auto mb-6 relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full animate-pulse opacity-20"></div>
                    <div className="absolute inset-2 bg-white rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 animate-spin text-orange-500" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="3"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
                    Creating Your Design
                  </h3>
                  <p className="text-gray-600 text-lg leading-relaxed">
                    Our AI is crafting something amazing for you...
                  </p>
                </div>

                {/* Progress Bar */}
                <div className="mb-6">
                  <div className="flex justify-between text-sm font-medium text-gray-700 mb-3">
                    <span>Progress</span>
                    <span className="text-orange-600">{Math.round(progress)}%</span>
                  </div>
                  <div className="w-full bg-gray-100 rounded-full h-4 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-orange-500 via-pink-500 to-purple-500 h-4 rounded-full transition-all duration-700 ease-out relative"
                      style={{ width: `${progress}%` }}
                    >
                      <div className="absolute inset-0 bg-white/30 animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Progress Messages */}
                <div className="text-base text-gray-600 font-medium mb-4">
                  {progress < 30 && "🎨 Analyzing your creative vision..."}
                  {progress >= 30 && progress < 60 && "🧠 AI is understanding your style..."}
                  {progress >= 60 && progress < 80 && "✨ Generating unique elements..."}
                  {progress >= 80 && "🎯 Adding final touches..."}
                </div>

                {/* Estimated time */}
                <div className="text-sm text-gray-500 bg-gray-50 rounded-lg py-2 px-4">
                  ⏱️ Estimated time: 30-45 seconds
                </div>
              </>
            ) : (
              <>
                {/* Error State */}
                <div className="mb-8">
                  <div className="w-20 h-20 mx-auto mb-6 relative">
                    <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
                    Server Busy
                  </h3>
                  <p className="text-gray-600 text-lg leading-relaxed mb-4">
                    Our servers are experiencing high demand right now.
                  </p>
                  <p className="text-base text-gray-500 mb-6 bg-orange-50 rounded-lg py-3 px-4">
                    🎨 Your amazing design will be worth the wait!
                  </p>
                </div>

                {/* Progress Bar - Stopped */}
                <div className="mb-8">
                  <div className="flex justify-between text-sm font-medium text-gray-700 mb-3">
                    <span>Progress</span>
                    <span className="text-gray-500">{Math.round(progress)}%</span>
                  </div>
                  <div className="w-full bg-gray-100 rounded-full h-4 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-gray-400 to-gray-500 h-4 rounded-full"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <button
                    onClick={handleCloseModal}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-6 rounded-xl font-semibold transition-all duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCloseModal}
                    className="flex-1 bg-gradient-to-r from-orange-500 to-pink-500 text-white py-3 px-6 rounded-xl font-semibold hover:from-orange-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    Try Again Later
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </section>
  );
}
