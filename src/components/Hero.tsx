'use client';

import React, { useState } from 'react';

export default function Hero() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showModal, setShowModal] = useState(false);

  const [showError, setShowError] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [instructions, setInstructions] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('');

  const styles = [
    { id: 'streetwear', name: 'Streetwear', icon: '🏙️' },
    { id: 'vintage', name: 'Vintage', icon: '📻' },
    { id: 'minimalist', name: 'Minimalist', icon: '⚪' },
    { id: 'typography', name: 'Typography', icon: '✍️' },
    { id: 'cartoon', name: 'Cartoon', icon: '🎨' },
    { id: 'abstract', name: 'Abstract', icon: '🌀' },
  ];

  const isFormValid = prompt.trim() && selectedStyle;

  const handleGenerate = () => {
    // Validate form
    if (!prompt.trim() || !selectedStyle) {
      return;
    }

    setIsGenerating(true);
    setProgress(0);
    setShowModal(true);
    setShowError(false);

    // Simulate realistic progress - fast at first, then slower
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev < 30) return prev + Math.random() * 8 + 5; // Fast start: 5-13% per step
        if (prev < 60) return prev + Math.random() * 4 + 2; // Medium: 2-6% per step
        if (prev < 80) return prev + Math.random() * 2 + 1; // Slow: 1-3% per step
        if (prev < 90) return prev + Math.random() * 1 + 0.5; // Very slow: 0.5-1.5% per step
        return prev; // Stop at around 90%
      });
    }, 600);

    // Wait 30-40 seconds before showing error
    const waitTime = 30000 + Math.random() * 10000; // 30-40 seconds
    setTimeout(() => {
      clearInterval(progressInterval);

      // Set final progress between 60-80%
      const finalProgress = 60 + Math.random() * 20;
      setProgress(finalProgress);

      // Show error message in modal after a short delay
      setTimeout(() => {
        setShowError(true);
      }, 1500);
    }, waitTime);
  };

  const handleCloseModal = () => {
    setIsGenerating(false);
    setShowModal(false);
    setShowError(false);
    setProgress(0);
  };
  const designStyles = [
    { name: 'Streetwear', color: 'bg-orange-100 text-orange-700 border-orange-200' },
    { name: 'Vintage', color: 'bg-amber-100 text-amber-700 border-amber-200' },
    { name: 'Minimalist', color: 'bg-blue-100 text-blue-700 border-blue-200' },
    { name: 'Typography', color: 'bg-purple-100 text-purple-700 border-purple-200' },
    { name: 'Cartoon', color: 'bg-pink-100 text-pink-700 border-pink-200' },
    { name: 'Abstract', color: 'bg-green-100 text-green-700 border-green-200' },
  ];

  return (
    <section className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20 lg:py-32 overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-orange-300 to-pink-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-gradient-to-r from-green-300 to-blue-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Call to Action Section - Left Right Layout */}
          <div className="grid lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
            {/* Left Side - Content */}
            <div className="space-y-8 text-left">
              <div>
                <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                  Create Stunning
                  <br />
                  <span className="bg-gradient-to-r from-orange-500 to-pink-500 bg-clip-text text-transparent">
                    T-Shirt Designs
                  </span>
                  <br />
                  with AI Magic
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                  Transform your ideas into stunning custom tshirt designs using our advanced design generator.
                  Experience multiple design styles, instant generation, and real-time preview powered by AI technology.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="#tshirt-designer"
                  className="bg-gradient-to-r from-orange-500 to-pink-500 text-white px-10 py-5 rounded-full font-bold text-xl hover:from-orange-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1 inline-block text-center"
                >
                  Try It Now
                </a>
                <button className="border-2 border-gray-300 text-gray-700 px-10 py-5 rounded-full font-bold text-xl hover:border-orange-500 hover:text-orange-500 transition-all duration-200">
                  Learn More
                </button>
              </div>

              {/* Features List */}
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-7 h-7 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm font-bold">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium text-lg">Powered by latest AI design models</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-7 h-7 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm font-bold">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium text-lg">6 unique design styles, supporting 50+ themes</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-7 h-7 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm font-bold">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium text-lg">Real-time design preview with instant download</span>
                </div>
              </div>
            </div>

            {/* Right Side - Interactive Form */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
              {/* Input Section */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Type or paste your text here (max 200 characters)...
                </label>
                <textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Describe your tshirt design idea for our tshirt design generator..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-0 focus:border-orange-500 resize-none transition-all duration-200 bg-white text-gray-900 placeholder-gray-400 hover:border-gray-400"
                  style={{ outline: 'none', boxShadow: 'none' }}
                  rows={3}
                  maxLength={200}
                />
              </div>

              {/* Instructions */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Add instructions to control design characteristics...
                </label>
                <input
                  type="text"
                  value={instructions}
                  onChange={(e) => setInstructions(e.target.value)}
                  placeholder="Example: 'Make it colorful with a retro vibe'"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-0 focus:border-orange-500 transition-all duration-200 bg-white text-gray-900 placeholder-gray-400 hover:border-gray-400"
                  style={{ outline: 'none', boxShadow: 'none' }}
                />
              </div>

              {/* Style Selection */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium text-gray-700">Design Style</label>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">Speed:</span>
                    <span className="text-sm font-medium">1x</span>
                  </div>
                </div>
                <select
                  value={selectedStyle}
                  onChange={(e) => setSelectedStyle(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-0 focus:border-orange-500 transition-all duration-200 bg-white text-gray-900 hover:border-gray-400"
                  style={{ outline: 'none', boxShadow: 'none' }}
                >
                  <option value="">Choose a style...</option>
                  {styles.map((style) => (
                    <option key={style.id} value={style.id}>
                      {style.icon} {style.name} Style
                    </option>
                  ))}
                </select>
              </div>

              {/* Generate Button */}
              <button
                onClick={handleGenerate}
                disabled={isGenerating || !isFormValid}
                className={`w-full py-4 px-6 rounded-xl font-bold text-lg transition-all duration-200 shadow-lg flex items-center justify-center space-x-2 ${
                  isGenerating || !isFormValid
                    ? 'bg-gray-400 cursor-not-allowed text-white'
                    : 'bg-gradient-to-r from-orange-500 to-pink-500 text-white hover:from-orange-600 hover:to-pink-600 hover:shadow-xl transform hover:-translate-y-1'
                }`}
              >
                {isGenerating ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Generating...</span>
                  </>
                ) : (
                  <span>🎨 Generate Tshirt Design</span>
                )}
              </button>

              {/* Free Notice */}
              <div className="mt-4 text-center">
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                  <span className="text-red-500">❤️</span>
                  <span>100% Free • No Sign-up Required</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Modal */}
      {showModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl p-8 max-w-md w-full mx-4 text-center shadow-2xl border border-white/20 relative">
            {/* Close Button */}
            <button
              onClick={handleCloseModal}
              className="absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 group"
            >
              <svg className="w-4 h-4 text-gray-500 group-hover:text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {!showError ? (
              <>
                <div className="mb-8">
                  <div className="w-20 h-20 mx-auto mb-6 relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full animate-pulse opacity-20"></div>
                    <div className="absolute inset-2 bg-white rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 animate-spin text-orange-500" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="3"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
                    Creating Your Design
                  </h3>
                  <p className="text-gray-600 text-lg leading-relaxed">
                    Our AI is crafting something amazing for you...
                  </p>
                </div>

                {/* Progress Bar */}
                <div className="mb-6">
                  <div className="flex justify-between text-sm font-medium text-gray-700 mb-3">
                    <span>Progress</span>
                    <span className="text-orange-600">{Math.round(progress)}%</span>
                  </div>
                  <div className="w-full bg-gray-100 rounded-full h-4 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-orange-500 via-pink-500 to-purple-500 h-4 rounded-full transition-all duration-700 ease-out relative"
                      style={{ width: `${progress}%` }}
                    >
                      <div className="absolute inset-0 bg-white/30 animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Progress Messages */}
                <div className="text-base text-gray-600 font-medium mb-4">
                  {progress < 30 && "🎨 Analyzing your creative vision..."}
                  {progress >= 30 && progress < 60 && "🧠 AI is understanding your style..."}
                  {progress >= 60 && progress < 80 && "✨ Generating unique elements..."}
                  {progress >= 80 && "🎯 Adding final touches..."}
                </div>

                {/* Estimated time */}
                <div className="text-sm text-gray-500 bg-gray-50 rounded-lg py-2 px-4">
                  ⏱️ Estimated time: 30-45 seconds
                </div>
              </>
            ) : (
              <>
                {/* Error State */}
                <div className="mb-8">
                  <div className="w-20 h-20 mx-auto mb-6 relative">
                    <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
                    Server Busy
                  </h3>
                  <p className="text-gray-600 text-lg leading-relaxed mb-4">
                    Our servers are experiencing high demand right now.
                  </p>
                  <p className="text-base text-gray-500 mb-6 bg-orange-50 rounded-lg py-3 px-4">
                    🎨 Your amazing design will be worth the wait!
                  </p>
                </div>

                {/* Progress Bar - Stopped */}
                <div className="mb-8">
                  <div className="flex justify-between text-sm font-medium text-gray-700 mb-3">
                    <span>Progress</span>
                    <span className="text-gray-500">{Math.round(progress)}%</span>
                  </div>
                  <div className="w-full bg-gray-100 rounded-full h-4 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-gray-400 to-gray-500 h-4 rounded-full"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <button
                    onClick={handleCloseModal}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-6 rounded-xl font-semibold transition-all duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCloseModal}
                    className="flex-1 bg-gradient-to-r from-orange-500 to-pink-500 text-white py-3 px-6 rounded-xl font-semibold hover:from-orange-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    Try Again Later
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </section>
  );
}
