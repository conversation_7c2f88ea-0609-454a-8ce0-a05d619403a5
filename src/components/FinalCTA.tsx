import Image from 'next/image';

export default function FinalCTA() {
  const ctaImages = [
    'https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?w=800&q=80',
    'https://images.unsplash.com/photo-1618005198919-d3d4b5a92ead?w=800&q=80',
    'https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=800&q=80',
    'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=800&q=80'
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6 tracking-tight">
            Start Creating Amazing AI Art
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-purple-600 to-blue-600 mx-auto mb-8"></div>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed mb-12">
            Join thousands of artists and creators using Seedream AI Image Generator.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <a
              href="#image-generator"
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-full hover:from-purple-700 hover:to-blue-700 transition-all duration-200 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              Start Creating
            </a>
            <a
              href="#gallery"
              className="bg-white text-slate-700 px-8 py-4 rounded-full border-2 border-slate-300 hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              Explore Gallery
            </a>
          </div>
        </div>

        {/* Featured Images Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
          {ctaImages.map((src, index) => (
            <div key={index} className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <Image
                src={src}
                alt={`Featured AI artwork ${index + 1}`}
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          ))}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-slate-900 mb-2">50K+</div>
            <div className="text-slate-600">Happy Users</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-slate-900 mb-2">1M+</div>
            <div className="text-slate-600">Images Created</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-slate-900 mb-2">10+</div>
            <div className="text-slate-600">AI Models</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-slate-900 mb-2">24/7</div>
            <div className="text-slate-600">Support</div>
          </div>
        </div>
      </div>
    </section>
  );
}
