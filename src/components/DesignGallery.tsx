import React from 'react';

export default function DesignGallery() {
  const galleryItems = [
    {
      id: 1,
      title: "Neon Cyberpunk",
      style: "Streetwear",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop",
      color: "from-purple-500 to-pink-500",
      description: "Futuristic cyberpunk design with neon elements"
    },
    {
      id: 2,
      title: "Retro Waves",
      style: "Vintage",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop",
      color: "from-orange-500 to-red-500",
      description: "80s inspired retro wave aesthetic"
    },
    {
      id: 3,
      title: "Mountain Silhouette",
      style: "Minimalist",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop",
      color: "from-blue-500 to-green-500",
      description: "Clean mountain landscape design"
    },
    {
      id: 4,
      title: "Space Explorer",
      style: "Cartoon",
      image: "https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=400&h=400&fit=crop",
      color: "from-pink-500 to-purple-500",
      description: "Cute space-themed cartoon illustration"
    },
    {
      id: 5,
      title: "Abstract Flow",
      style: "Abstract",
      image: "https://images.unsplash.com/photo-1541701494587-cb58502866ab?w=400&h=400&fit=crop",
      color: "from-green-500 to-blue-500",
      description: "Flowing abstract shapes and patterns"
    },
    {
      id: 6,
      title: "Bold Typography",
      style: "Typography",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop",
      color: "from-red-500 to-orange-500",
      description: "Strong typographic statement design"
    },
    {
      id: 7,
      title: "Ocean Vibes",
      style: "Vintage",
      image: "https://images.unsplash.com/photo-1505142468610-359e7d316be0?w=400&h=400&fit=crop",
      color: "from-blue-500 to-teal-500",
      description: "Vintage ocean and surf culture"
    },
    {
      id: 8,
      title: "Geometric Lion",
      style: "Minimalist",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop",
      color: "from-yellow-500 to-orange-500",
      description: "Geometric animal illustration"
    }
  ];

  return (
    <section id="gallery" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
            Tshirt Design Gallery
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
            Explore our collection of stunning designs created with our tshirt design generator.
            From minimalist to bold styles, find inspiration for your next creation.
          </p>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {galleryItems.map((item) => (
            <div key={item.id} className="group cursor-pointer">
              <div className="relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                {/* Image Container */}
                <div className="relative h-80 overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />

                  {/* Gradient Overlay */}
                  <div className={`absolute inset-0 bg-gradient-to-t ${item.color} opacity-20 group-hover:opacity-30 transition-opacity duration-300`}></div>

                  {/* Style Badge */}
                  <div className="absolute top-4 left-4">
                    <span className={`px-3 py-1 rounded-full text-xs font-bold text-white bg-gradient-to-r ${item.color} shadow-lg backdrop-blur-sm`}>
                      {item.style}
                    </span>
                  </div>

                  {/* Hover Info Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                    <div className="text-center text-white p-6">
                      <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                      <p className="text-sm opacity-90 mb-4">{item.description}</p>
                      <div className="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 rounded-full backdrop-blur-sm">
                        <span className="text-sm font-medium">View Design</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Info Section */}
                <div className="p-6">
                  <h3 className="font-bold text-gray-900 text-lg mb-2 group-hover:text-gray-700 transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {item.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-12 shadow-xl border border-white/20">
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              Ready to Create Your Own Design?
            </h3>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
              Get inspired by these amazing designs and create your own unique t-shirt with our AI-powered design tool.
            </p>

            <a
              href="#tshirt-designer"
              className="inline-flex items-center bg-gradient-to-r from-orange-500 to-pink-500 text-white px-10 py-5 rounded-full hover:from-orange-600 hover:to-pink-600 transition-all duration-200 font-bold text-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              <span className="mr-2">🎨</span>
              Start Creating Now
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
