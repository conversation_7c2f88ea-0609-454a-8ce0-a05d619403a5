import Image from 'next/image';

export default function ArtStyles() {
  const artStyles = [
    {
      id: 1,
      name: 'Natural',
      image: 'https://images.unsplash.com/photo-1518495973542-4542c06a5843?q=80&w=1974&auto=format&fit=crop',
      description: 'Realistic and natural-looking images'
    },
    {
      id: 2,
      name: 'Cinematic',
      image: 'https://images.unsplash.com/photo-1472396961693-142e6e269027?q=80&w=2152&auto=format&fit=crop',
      description: 'Movie-like dramatic scenes'
    },
    {
      id: 3,
      name: 'Anime',
      image: 'https://images.unsplash.com/photo-1505142468610-359e7d316be0?q=80&w=2126&auto=format&fit=crop',
      description: 'Japanese animation style'
    },
    {
      id: 4,
      name: 'Digital Art',
      image: 'https://images.unsplash.com/photo-1482881497185-d4a9ddbe4151?q=80&w=1965&auto=format&fit=crop',
      description: 'Modern digital artwork'
    },
    {
      id: 5,
      name: 'Oil Painting',
      image: 'https://plus.unsplash.com/premium_photo-1673264933212-d78737f38e48?q=80&w=1974&auto=format&fit=crop',
      description: 'Classic oil painting style'
    },
    {
      id: 6,
      name: 'Watercolor',
      image: 'https://plus.unsplash.com/premium_photo-1711434824963-ca894373272e?q=80&w=2030&auto=format&fit=crop',
      description: 'Soft watercolor paintings'
    },
    {
      id: 7,
      name: 'Vintage',
      image: 'https://plus.unsplash.com/premium_photo-1675705721263-0bbeec261c49?q=80&w=1940&auto=format&fit=crop',
      description: 'Retro and vintage aesthetics'
    },
    {
      id: 8,
      name: 'Cyberpunk',
      image: 'https://images.unsplash.com/photo-1524799526615-766a9833dec0?q=80&w=1935&auto=format&fit=crop',
      description: 'Futuristic cyberpunk style'
    }
  ];

  return (
    <section id="styles" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Art Styles
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore various AI art styles and find the perfect aesthetic for your creative projects
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {artStyles.map((style) => (
            <div key={style.id} className="group cursor-pointer">
              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2">
                <Image
                  src={style.image}
                  alt={`${style.name} style`}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 className="font-bold text-lg mb-1">{style.name}</h3>
                  <p className="text-sm text-gray-200">{style.description}</p>
                </div>
                <div className="absolute inset-0 bg-purple-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <button className="bg-white text-purple-600 px-6 py-2 rounded-full font-medium transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                    Use Style
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Duplicate row for infinite scroll effect */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-6">
          {artStyles.map((style) => (
            <div key={`duplicate-${style.id}`} className="group cursor-pointer">
              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2">
                <Image
                  src={style.image}
                  alt={`${style.name} style`}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 className="font-bold text-lg mb-1">{style.name}</h3>
                  <p className="text-sm text-gray-200">{style.description}</p>
                </div>
                <div className="absolute inset-0 bg-purple-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <button className="bg-white text-purple-600 px-6 py-2 rounded-full font-medium transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                    Use Style
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
