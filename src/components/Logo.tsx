import React from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'white' | 'dark';
  showText?: boolean;
  className?: string;
}

export default function Logo({ 
  size = 'md', 
  variant = 'default', 
  showText = true, 
  className = '' 
}: LogoProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  };

  const textColorClasses = {
    default: 'text-gray-900',
    white: 'text-white',
    dark: 'text-gray-900'
  };

  return (
    <div className={`flex items-center ${className}`}>
      {/* Logo Icon */}
      <div className={`${sizeClasses[size]} bg-gradient-to-br from-orange-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg relative overflow-hidden`}>
        {/* T-Shirt Shape */}
        <svg 
          viewBox="0 0 24 24" 
          className="w-6 h-6 text-white"
          fill="currentColor"
        >
          <path d="M16 4h1.5c.83 0 1.5.67 1.5 1.5S18.33 7 17.5 7H16v13c0 .55-.45 1-1 1H9c-.55 0-1-.45-1-1V7H6.5C5.67 7 5 6.33 5 5.5S5.67 4 6.5 4H8V3c0-.55.45-1 1-1h6c.55 0 1 .45 1 1v1zm-6 0h4V3h-4v1z"/>
        </svg>
        
        {/* AI Sparkle Effect */}
        <div className="absolute top-1 right-1">
          <svg className="w-2 h-2 text-yellow-300" fill="currentColor" viewBox="0 0 8 8">
            <path d="M4 0L5 3L8 4L5 5L4 8L3 5L0 4L3 3L4 0Z"/>
          </svg>
        </div>
      </div>

      {/* Brand Text */}
      {showText && (
        <div className="ml-3">
          <span className={`${textSizeClasses[size]} font-bold ${textColorClasses[variant]} tracking-tight`}>
            AI Shirt
          </span>
        </div>
      )}
    </div>
  );
}

// Logo variants for different use cases
export function LogoIcon({ size = 'md', className = '' }: { size?: 'sm' | 'md' | 'lg', className?: string }) {
  return <Logo size={size} showText={false} className={className} />;
}

export function LogoWithText({ size = 'md', variant = 'default', className = '' }: { 
  size?: 'sm' | 'md' | 'lg', 
  variant?: 'default' | 'white' | 'dark',
  className?: string 
}) {
  return <Logo size={size} variant={variant} showText={true} className={className} />;
}
