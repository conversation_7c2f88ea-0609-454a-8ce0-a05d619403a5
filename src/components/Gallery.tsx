import Image from 'next/image';

export default function Gallery() {
  const galleryImages = [
    {
      id: 1,
      src: 'https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?w=800&q=80',
      alt: 'Futuristic cityscape with glowing skyscrapers and flying cars',
      model: 'DALL-E 3',
      prompt: 'A futuristic cityscape with glowing skyscrapers and flying cars',
      fullPrompt: 'A futuristic cityscape with glowing skyscrapers and flying cars, neon lights reflecting on wet streets, cyberpunk aesthetic, ultra detailed, 8k resolution'
    },
    {
      id: 2,
      src: 'https://images.unsplash.com/photo-1558470598-a5dda9640f68?w=800&q=80',
      alt: 'Dreamy purple forest with glowing mushrooms and mysterious light',
      model: 'Flux Pro',
      prompt: 'Dreamy purple forest with glowing mushrooms and mysterious light',
      fullPrompt: 'A dreamy purple forest with bioluminescent mushrooms casting ethereal light, mystical atmosphere, fantasy art style, highly detailed'
    },
    {
      id: 3,
      src: 'https://images.unsplash.com/photo-1618005198919-d3d4b5a92ead?w=800&q=80',
      alt: 'Galaxy spiral in space, colorful and mysterious',
      model: 'Kling 1.6',
      prompt: 'Galaxy spiral in space, colorful and mysterious',
      fullPrompt: 'A magnificent spiral galaxy in deep space, vibrant colors of purple, blue and gold, cosmic dust and stars, astronomical photography style'
    },
    {
      id: 4,
      src: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=800&q=80',
      alt: 'Ancient temple ruins covered with vines, sunlight streaming through gaps',
      model: 'Jimeng V1.1',
      prompt: 'Ancient temple ruins covered with vines, sunlight streaming through gaps',
      fullPrompt: 'Ancient stone temple ruins overgrown with lush green vines, golden sunlight filtering through cracks, atmospheric lighting, photorealistic'
    },
    {
      id: 5,
      src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&q=80',
      alt: 'Snow-covered mountain peaks, aurora dancing in the sky',
      model: 'Flux Dev',
      prompt: 'Snow-covered mountain peaks, aurora dancing in the sky',
      fullPrompt: 'Majestic snow-covered mountain peaks under dancing aurora borealis, green and purple lights in starry night sky, landscape photography'
    },
    {
      id: 6,
      src: 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=800&q=80',
      alt: 'Tropical beach sunset, coconut tree silhouettes against orange-red sky',
      model: 'DALL-E 2',
      prompt: 'Tropical beach sunset, coconut tree silhouettes against orange-red sky',
      fullPrompt: 'Tropical beach at sunset with palm tree silhouettes against vibrant orange and red sky, peaceful ocean waves, paradise scene'
    }
  ];

  return (
    <section id="gallery" className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 tracking-tight">
            Gallery
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Explore amazing artworks created by artificial intelligence, each one is a unique creative expression
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {galleryImages.map((image) => (
            <div key={image.id} className="bg-gray-900 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-700">
              <div className="relative aspect-square">
                <Image
                  src={image.src}
                  alt={image.alt}
                  fill
                  className="object-cover"
                />
              </div>

              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="inline-block bg-gradient-to-r from-purple-900/50 to-blue-900/50 text-purple-300 px-3 py-1.5 rounded-full text-sm font-semibold border border-purple-700">
                    {image.model}
                  </span>
                </div>

                <h3 className="font-semibold text-white mb-2 text-lg">
                  {image.prompt}
                </h3>

                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-200 mb-1">Full Prompt</h4>
                  <p className="text-sm text-gray-400 line-clamp-3 leading-relaxed">
                    {image.fullPrompt}
                  </p>
                </div>

                <button className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-2.5 px-4 rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl">
                  Use Prompt
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button className="bg-white text-gray-700 px-8 py-3 rounded-full border border-gray-300 hover:bg-gray-50 transition-all duration-200 font-medium shadow-lg hover:shadow-xl">
            View More Gallery
          </button>
        </div>
      </div>
    </section>
  );
}
