"use client";

import { useEffect } from "react";

/**
 * Loads the SocialBar sticky script once per app.
 */
export default function SocialBar() {
  useEffect(() => {
    const SCRIPT_ID = "ad-script-socialbar-d0d7d44e";
    if (document.getElementById(SCRIPT_ID)) return;

    const s = document.createElement("script");
    s.id = SCRIPT_ID;
    s.src = "https://pl27397024.profitableratecpm.com/d0/d7/d4/d0d7d44e35bfa759f93f516bd578c074.js";
    s.async = false; // provider specifies sync
    document.body.appendChild(s);
  }, []);

  return null;
}

