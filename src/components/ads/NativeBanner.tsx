"use client";

import { useEffect } from "react";

/**
 * Renders a native banner ad.
 * - Injects the provider script once on mount
 * - Renders the required container div
 * - Only runs in production to avoid noise during local dev
 */
export default function NativeBanner() {
  useEffect(() => {
    if (process.env.NODE_ENV !== "production") return;

    const SCRIPT_ID = "ad-script-native-8bdb46ad";
    if (document.getElementById(SCRIPT_ID)) return; // avoid duplicate

    const s = document.createElement("script");
    s.id = SCRIPT_ID;
    s.src = "https://pl27397027.profitableratecpm.com/8bdb46ad7963c2e813a35c82e728c9de/invoke.js";
    s.async = true;
    // @ts-ignore allow custom attribute
    s.setAttribute("data-cfasync", "false");
    document.body.appendChild(s);

    return () => {
      // keep script for SPA navigation; do not remove on unmount
    };
  }, []);

  return (
    <div className="my-8">
      <div className="text-xs uppercase tracking-wide text-gray-400 mb-2">Sponsored</div>
      <div id="container-8bdb46ad7963c2e813a35c82e728c9de" className="w-full" />
    </div>
  );
}

