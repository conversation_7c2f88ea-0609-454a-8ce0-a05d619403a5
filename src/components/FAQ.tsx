'use client';

import React, { useState } from 'react';

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: 'What is an AI Tshirt Design Generator?',
      answer: 'An AI tshirt design generator uses artificial intelligence to create custom tshirt designs based on your ideas or prompts. Our tshirt design generator can input your preferences, and the AI generates a unique tshirt design for you in seconds.'
    },
    {
      question: 'Do I Need Design Skills to Use This Tshirt Design Generator?',
      answer: 'Not at all! Our design generator is designed for everyone, from complete beginners to professional designers. Simply describe what you want in plain English, choose a style, and our AI will create a professional-looking design for you.'
    },
    {
      question: 'Can I Customize the AI-Generated Tshirt Designs?',
      answer: 'Yes! You can customize various aspects of your design including style, colors, and themes. Our generator allows you to provide detailed instructions to guide the AI in creating exactly what you envision.'
    },
    {
      question: 'How Long Does It Take to Generate a Tshirt Design?',
      answer: 'Our AI generator creates designs in under 10 seconds! Just describe your idea, choose a style, and your custom design will be ready almost instantly. No waiting in queues or long processing times.'
    },
    {
      question: 'Can I Use This Tshirt Design Generator on My Phone?',
      answer: 'Absolutely! Our generator is fully mobile-friendly and works perfectly on smartphones and tablets. You can create amazing designs anywhere, anytime, directly from your mobile device.'
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faq" className="py-20 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 tracking-tight">
            FAQs about Text to T-Shirt Design AI Generator
          </h2>
        </div>

        {/* FAQ List */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div key={index} className="transition-all duration-200">
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full text-left flex items-center justify-between group py-4 px-4 transition-all duration-200 focus:outline-none focus:ring-0 focus:border-none focus:shadow-none border-none outline-none"
                style={{ outline: 'none', border: 'none', boxShadow: 'none' }}
              >
                <h3 className="text-lg font-semibold text-gray-900 pr-8 group-hover:text-gray-700">
                  {faq.question}
                </h3>
                <div className="flex-shrink-0">
                  <svg
                    className={`w-5 h-5 text-gray-500 transition-transform duration-300 ease-in-out ${
                      openIndex === index ? 'rotate-180' : 'rotate-0'
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>

              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  openIndex === index ? 'max-h-96 opacity-100 pb-4' : 'max-h-0 opacity-0'
                }`}
              >
                <div className="text-gray-600 leading-relaxed px-4">
                  {faq.answer}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-orange-500 to-pink-500 rounded-2xl p-8 md:p-10 text-white shadow-lg">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Create Your Perfect T-Shirt Design?
            </h3>
            <p className="text-lg mb-6 text-orange-100 max-w-xl mx-auto">
              Join thousands of creators who have already designed amazing t-shirts with our free AI tool.
            </p>

            <a
              href="#tshirt-designer"
              className="inline-flex items-center bg-white text-orange-600 px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-200 font-bold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              <span className="mr-2">🎨</span>
              Start Creating Now
            </a>

            <div className="mt-4 text-sm text-orange-100">
              ✨ No sign-up required • 100% Free forever • Download instantly
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
