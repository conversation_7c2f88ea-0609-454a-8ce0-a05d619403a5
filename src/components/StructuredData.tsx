export default function StructuredData() {
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "AI Shirt",
    "description": "Create stunning custom t-shirt designs with AI. Free t-shirt design generator - no skills needed. Just describe your idea and download instantly!",
    "url": "https://aishirt.app",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://aishirt.app/?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  };

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AI Shirt",
    "description": "AI-powered t-shirt design platform",
    "url": "https://aishirt.app",
    "logo": "https://aishirt.app/logo-512.svg",
    "sameAs": [
      "https://twitter.com/aishirt",
      "https://facebook.com/aishirt",
      "https://instagram.com/aishirt"
    ]
  };

  const softwareApplicationSchema = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "AI Shirt T-Shirt Design Generator",
    "description": "Free AI-powered t-shirt design generator that creates custom designs from text descriptions",
    "url": "https://aishirt.app",
    "applicationCategory": "DesignApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "1250",
      "bestRating": "5",
      "worstRating": "1"
    },
    "featureList": [
      "AI-powered design generation",
      "Multiple design styles",
      "Instant preview",
      "High-resolution downloads",
      "Commercial usage rights"
    ]
  };

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "What is an AI T-Shirt Design Generator?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "An AI T-shirt design generator uses artificial intelligence to create custom t-shirt designs based on your ideas or prompts. You can input your preferences, and the AI generates a unique design for you in seconds."
        }
      },
      {
        "@type": "Question",
        "name": "Do I Need Design Skills to Use This Tool?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "No design skills are required! Our AI-powered tool is designed for everyone. Simply describe what you want, choose your style preferences, and let our AI create professional-quality designs for you."
        }
      },
      {
        "@type": "Question",
        "name": "Can I Customize the AI-Generated T-Shirt Designs?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes! You can customize colors, styles, text, and other design elements. Our tool offers various customization options to make your design truly unique."
        }
      },
      {
        "@type": "Question",
        "name": "How Long Does It Take to Generate a T-Shirt Design?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Our AI generates t-shirt designs in just a few seconds. The entire process from input to final design typically takes less than 30 seconds."
        }
      },
      {
        "@type": "Question",
        "name": "Can I Use This T-Shirt Design Generator on My Phone?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes! Our t-shirt design generator is fully responsive and works perfectly on mobile devices, tablets, and desktop computers."
        }
      }
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteSchema) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(softwareApplicationSchema) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
      />
    </>
  );
}
