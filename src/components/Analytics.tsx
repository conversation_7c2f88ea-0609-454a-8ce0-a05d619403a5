'use client';

import { useEffect } from 'react';

export default function Analytics() {
  useEffect(() => {
    // 页面加载性能监控
    if (typeof window !== 'undefined' && 'performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          
          if (perfData) {
            console.log('页面性能指标:', {
              DNS查询时间: perfData.domainLookupEnd - perfData.domainLookupStart,
              TCP连接时间: perfData.connectEnd - perfData.connectStart,
              请求响应时间: perfData.responseEnd - perfData.requestStart,
              DOM解析时间: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
              页面加载总时间: perfData.loadEventEnd - perfData.fetchStart,
            });
          }
        }, 0);
      });
    }

    // 用户交互追踪
    const trackInteraction = (event: string, element: string) => {
      console.log(`用户交互: ${event} - ${element}`);
      // 这里可以集成 Google Analytics, Mixpanel 等分析工具
    };

    // 监听重要的用户交互
    const buttons = document.querySelectorAll('button, a[href="#image-generator"]');
    buttons.forEach(button => {
      button.addEventListener('click', () => {
        trackInteraction('click', button.textContent || 'unknown');
      });
    });

    // 监听滚动事件（节流）
    let scrollTimeout: NodeJS.Timeout;
    const handleScroll = () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        if (scrollPercent % 25 === 0) { // 每25%记录一次
          trackInteraction('scroll', `${scrollPercent}%`);
        }
      }, 100);
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, []);

  return null; // 这个组件不渲染任何内容
}
