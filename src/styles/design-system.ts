// 暗黑主题设计系统规范 - 基于 image-generator.coreychiu.com 分析
export const designSystem = {
  // 暗黑配色方案
  colors: {
    // 暗黑主色调
    dark: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      850: '#1f1f1f',
      900: '#171717',
      925: '#111111',
      950: '#0a0a0a',
      975: '#050505',
      1000: '#000000',
    },
    // 紫色系 (品牌色)
    purple: {
      50: '#faf5ff',
      100: '#f3e8ff',
      200: '#e9d5ff',
      300: '#d8b4fe',
      400: '#c084fc',
      500: '#a855f7',
      600: '#9333ea',
      700: '#7c3aed',
      800: '#6b21a8',
      900: '#581c87',
    },
    // 蓝色系 (辅助色)
    blue: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },
    // 暗黑主题渐变色
    gradients: {
      primary: 'linear-gradient(135deg, #9333ea 0%, #3b82f6 100%)',
      darkBg: 'linear-gradient(135deg, #000000 0%, #111111 50%, #0a0a0a 100%)',
      cardBg: 'linear-gradient(135deg, #1f1f1f 0%, #2a2a2a 100%)',
      heroBg: 'linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #111111 100%)',
    }
  },

  // 排版系统
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', '-apple-system', 'sans-serif'],
      mono: ['JetBrains Mono', 'Menlo', 'Monaco', 'monospace'],
    },
    fontSize: {
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      '5xl': ['3rem', { lineHeight: '1' }],
      '6xl': ['3.75rem', { lineHeight: '1' }],
      '7xl': ['4.5rem', { lineHeight: '1' }],
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    }
  },

  // 间距系统
  spacing: {
    xs: '0.5rem',    // 8px
    sm: '1rem',      // 16px
    md: '1.5rem',    // 24px
    lg: '2rem',      // 32px
    xl: '3rem',      // 48px
    '2xl': '4rem',   // 64px
    '3xl': '5rem',   // 80px
    '4xl': '6rem',   // 96px
    '5xl': '8rem',   // 128px
  },

  // 圆角系统
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    base: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px',
  },

  // 阴影系统
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  },

  // 动画系统
  animations: {
    transition: {
      fast: '150ms ease-in-out',
      base: '200ms ease-in-out',
      slow: '300ms ease-in-out',
    },
    hover: {
      scale: 'scale(1.02)',
      translateY: 'translateY(-2px)',
      shadow: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    }
  },

  // 暗黑主题组件规范
  components: {
    button: {
      primary: {
        background: 'linear-gradient(135deg, #9333ea 0%, #3b82f6 100%)',
        color: '#ffffff',
        padding: '0.75rem 2rem',
        borderRadius: '9999px',
        fontWeight: '600',
        fontSize: '1rem',
        transition: 'all 200ms ease-in-out',
        hover: {
          transform: 'translateY(-2px)',
          boxShadow: '0 20px 25px -5px rgb(147 51 234 / 0.3), 0 8px 10px -6px rgb(59 130 246 / 0.3)',
        }
      },
      secondary: {
        background: '#1f1f1f',
        color: '#e5e5e5',
        border: '1px solid #404040',
        padding: '0.75rem 2rem',
        borderRadius: '9999px',
        fontWeight: '600',
        fontSize: '1rem',
        transition: 'all 200ms ease-in-out',
        hover: {
          background: '#2a2a2a',
          borderColor: '#525252',
          transform: 'translateY(-2px)',
        }
      }
    },
    card: {
      background: '#1f1f1f',
      borderRadius: '1rem',
      border: '1px solid #333333',
      boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3)',
      padding: '1.5rem',
      transition: 'all 200ms ease-in-out',
      hover: {
        background: '#2a2a2a',
        borderColor: '#404040',
        boxShadow: '0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4)',
        transform: 'translateY(-4px)',
      }
    },
    input: {
      background: '#111111',
      border: '1px solid #333333',
      borderRadius: '0.5rem',
      padding: '0.75rem 1rem',
      fontSize: '1rem',
      color: '#ffffff',
      transition: 'all 200ms ease-in-out',
      focus: {
        borderColor: '#9333ea',
        boxShadow: '0 0 0 3px rgb(147 51 234 / 0.2)',
        background: '#1a1a1a',
      }
    }
  }
};

// 响应式断点
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

// 内容规范 (基于 seedream.pro)
export const contentGuidelines = {
  tone: {
    friendly: true,
    professional: true,
    accessible: true,
    encouraging: true,
  },
  messaging: {
    primary: "Transform your ideas into stunning AI art—instantly and for free",
    features: [
      "Free & Unlimited Access",
      "Multiple Artistic Styles", 
      "Smart Customization",
      "Super-Resolution Support",
      "Fast & Intuitive"
    ],
    cta: {
      primary: "Start Creating",
      secondary: "Try for Free",
      tertiary: "Explore Gallery"
    }
  }
};
