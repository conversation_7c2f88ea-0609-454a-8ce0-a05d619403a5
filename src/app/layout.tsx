import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import ErrorBoundary from "@/components/ErrorBoundary";
import StructuredData from "@/components/StructuredData";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL('https://aishirt.app/'),
  title: {
    default: "AI T-Shirt Design Generator - Free Custom Maker Online",
    template: "%s | AI Shirt"
  },
  description: "Create stunning custom t-shirt designs with AI. Free t-shirt design generator - no skills needed. Just describe your idea and download instantly!",
  authors: [{ name: "AI Shirt", url: "https://aishirt.app/" }],
  creator: "AI Shirt",
  publisher: "AI Shirt",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: '/icon.svg',
    shortcut: '/icon.svg',
    apple: '/icon.svg',
  },
  openGraph: {
    title: "AI T-Shirt Design Generator - Free Custom Maker Online",
    description: "Create stunning custom t-shirt designs with AI. Free t-shirt design generator - no skills needed. Just describe your idea and download instantly!",
    url: "https://aishirt.app/",
    siteName: "AI Shirt",
    images: [
      {
        url: "/og-aishirt-design.jpg",
        width: 1200,
        height: 630,
        alt: "AI Shirt - Free AI T-Shirt Design Generator",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI T-Shirt Design Generator - Free Custom Maker Online",
    description: "Create stunning custom t-shirt designs with AI. Free generator - no skills needed. Just describe and download!",
    images: ["/og-aishirt-design.jpg"],
    creator: "@aishirt",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://aishirt.app/",
  },
  category: "Design Tools",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.svg?v=4" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/favicon.svg?v=4" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#f97316" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="AI Shirt" />
        <meta name="msvalidate.01" content="AB2D854F61F9515035A979630086B6F2" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <StructuredData />
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      </body>
    </html>
  );
}
