# 🚀 Vercel 部署指南

## 📋 部署前检查清单

### ✅ 已完成的准备工作
- [x] 项目构建成功 (`npm run build`)
- [x] 创建了 `.gitignore` 文件
- [x] 创建了 `vercel.json` 配置文件
- [x] 创建了 `.env.example` 环境变量示例
- [x] 修复了所有 TypeScript 错误
- [x] 优化了表单边框样式
- [x] 配置了图片域名白名单

## 🌐 部署到 Vercel

### 方法一：通过 Vercel CLI（推荐）

1. **安装 Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **登录 Vercel**
   ```bash
   vercel login
   ```

3. **部署项目**
   ```bash
   vercel
   ```
   
   首次部署时会询问：
   - Set up and deploy? → `Y`
   - Which scope? → 选择您的账户
   - Link to existing project? → `N`
   - What's your project's name? → `t-shirt-design-generator`
   - In which directory is your code located? → `./`

4. **生产环境部署**
   ```bash
   vercel --prod
   ```

### 方法二：通过 GitHub 集成

1. **推送代码到 GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin https://github.com/yourusername/t-shirt-design-generator.git
   git push -u origin main
   ```

2. **在 Vercel 控制台导入**
   - 访问 [vercel.com](https://vercel.com)
   - 点击 "New Project"
   - 从 GitHub 导入您的仓库
   - 配置项目设置（通常自动检测）
   - 点击 "Deploy"

## ⚙️ 环境变量配置

在 Vercel 控制台中设置以下环境变量：

### 必需的环境变量
```
NEXT_PUBLIC_SITE_URL=https://your-domain.vercel.app
NEXT_PUBLIC_SITE_NAME=T-Shirt Design Generator
```

### 可选的环境变量
```
NEXT_PUBLIC_GA_ID=your_google_analytics_id
NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id
```

## 🔧 项目配置详情

### Next.js 配置 (`next.config.ts`)
- ✅ 配置了图片域名白名单
- ✅ 支持 Unsplash 和其他图片服务

### Vercel 配置 (`vercel.json`)
- ✅ 优化了亚洲地区部署（香港、新加坡、东京）
- ✅ 配置了安全头部
- ✅ 设置了 API 跨域支持
- ✅ 配置了 sitemap 和 robots.txt 重写

### 构建配置
- ✅ 使用 Next.js 15.2.3
- ✅ 支持 TypeScript
- ✅ 使用 Tailwind CSS 4.0
- ✅ 静态生成优化

## 📊 性能优化

### 已实现的优化
- ✅ 静态页面生成 (SSG)
- ✅ 图片优化 (Next.js Image)
- ✅ CSS 优化 (Tailwind CSS)
- ✅ 代码分割
- ✅ 压缩和缓存

### 构建结果
```
Route (app)                Size    First Load JS
┌ ○ /                     6.77 kB    107 kB
├ ○ /_not-found           978 B      101 kB  
├ ○ /robots.txt           139 B      101 kB
└ ○ /sitemap.xml          139 B      101 kB
```

## 🌍 域名配置

### 自定义域名
1. 在 Vercel 控制台进入项目设置
2. 点击 "Domains" 标签
3. 添加您的自定义域名
4. 按照指示配置 DNS 记录

### SSL 证书
- Vercel 自动提供免费 SSL 证书
- 支持自动续期

## 📈 监控和分析

### 内置功能
- ✅ Vercel Analytics（免费）
- ✅ 性能监控
- ✅ 错误追踪

### 可选集成
- Google Analytics
- Hotjar 用户行为分析
- Sentry 错误监控

## 🚨 常见问题

### 构建失败
- 检查 TypeScript 错误
- 确保所有依赖都已安装
- 检查环境变量配置

### 图片加载问题
- 确保图片域名在 `next.config.ts` 中配置
- 检查图片 URL 是否正确

### 样式问题
- 确保 Tailwind CSS 正确配置
- 检查 CSS 文件导入

## 📞 支持

如果遇到问题：
1. 检查 Vercel 部署日志
2. 查看浏览器控制台错误
3. 参考 [Vercel 文档](https://vercel.com/docs)
4. 参考 [Next.js 文档](https://nextjs.org/docs)

## 🎉 部署完成后

部署成功后，您的网站将可以通过以下方式访问：
- Vercel 提供的 URL: `https://your-project.vercel.app`
- 自定义域名（如果配置）

记得测试所有功能：
- ✅ 页面加载
- ✅ 表单交互
- ✅ 图片显示
- ✅ 响应式设计
- ✅ SEO 元数据
